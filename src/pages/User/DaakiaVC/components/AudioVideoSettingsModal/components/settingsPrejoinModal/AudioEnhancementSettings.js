import React from 'react';
import { Switch } from 'antd';

/**
 * AudioEnhancementSettings Component
 * Audio enhancement controls (noise cancellation, echo cancellation, auto-mute)
 * @param {boolean} noiseCancellation - Noise cancellation state
 * @param {function} onNoiseCancellationChange - Noise cancellation change handler
 * @param {boolean} echoCancellation - Echo cancellation state
 * @param {function} onEchoCancellationChange - Echo cancellation change handler
 * @param {boolean} autoMuteOnJoin - Auto-mute on join state
 * @param {function} onAutoMuteOnJoinChange - Auto-mute on join change handler
 */
function AudioEnhancementSettings({
  noiseCancellation = false,
  onNoiseCancellationChange,
  echoCancellation = false,
  onEchoCancellationChange,
  autoMuteOnJoin = true,
  onAutoMuteOnJoinChange
}) {
  return (
    <div className="settings-section">
      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Noise cancellation</span>
            <span className="setting-sublabel">Remove background noise to improve call quality</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={noiseCancellation}
            onChange={onNoiseCancellationChange}
          />
          <span className="switch-status">
            {noiseCancellation ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
      <div className="setting-border" />

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Echo cancellation</span>
            <span className="setting-sublabel">Remove background noise to improve call quality</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={echoCancellation}
            onChange={onEchoCancellationChange}
          />
          <span className="switch-status">
            {echoCancellation ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
      <div className="setting-border" />

      <div className="setting-row">
        <div className="setting-info">
          <div className="setting-details">
            <span className="setting-label">Auto-mute on joining meeting</span>
            <span className="setting-sublabel">Remove background noise to improve call quality</span>
          </div>
        </div>
        <div className="setting-control">
          <Switch
            checked={autoMuteOnJoin}
            onChange={onAutoMuteOnJoinChange}
          />
          <span className="switch-status">
            {autoMuteOnJoin ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      </div>
    </div>
  );
}

export default AudioEnhancementSettings;
